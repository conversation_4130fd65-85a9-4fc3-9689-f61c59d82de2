# Ignore common development files
.git
.gitignore
.vscode
.idea
.DS_Store
Thumbs.db

# Ignore documentation
README.md
*.md
CHANGELOG.md

# Ignore build artifacts (but keep pubspec.lock for reproducible builds)
build/
.dart_tool/
.packages
.pub-cache/

# Ignore Flutter specific files (for client builds)
.flutter-plugins
.flutter-plugins-dependencies
.metadata

# Ignore node modules if any
node_modules/

# Ignore Docker files when building
Dockerfile*
docker-compose*.yml
.dockerignore

# Ignore local environment files
.env
.env.local
.env.development
.env.test
.env.production

# Ignore coverage reports
coverage/

# Allow test files for now (can be excluded in production builds)
# test/
# *_test.dart
# **/*_test.dart

# Ignore analysis files
analysis_options.yaml

# Ignore platform specific folders when not needed
android/
ios/
linux/
macos/
windows/

# Ignore Flutter ephemeral files and symlinks that cause Docker issues on Windows
**/flutter/ephemeral/
**/.plugin_symlinks/
**/Generated.xcconfig
**/flutter_export_environment.sh
